import React, { useState, useEffect, useRef } from 'react';
import { Popover } from '@mui/material';
import clsx from 'clsx';
import { formatToTwoDecimalPlaces } from '@bryzos/giss-ui-library';
import styles from './PriceHistoryDropdown.module.scss';
import { ReactComponent as RedArrow } from '../../assets/New-images/New-Image-latest/redArrow.svg';
import { ReactComponent as GreenArrow } from '../../assets/New-images/New-Image-latest/geenArrow.svg';
import { CustomMenu } from 'src/renderer2/pages/buyer/CustomMenu';

interface PriceHistoryItem {
  created_date?: string;
  qty?: string | number;
  qty_unit?: string;
  buyer_price_per_unit?: string | number;
  price_unit?: string;
  buyer_line_total?: string | number;
}

interface PriceChangeIndicator {
  show: boolean;
  direction: React.ReactNode;
  color: string;
  changeAmount: number;
}

interface PriceHistoryDropdownProps {
  lineHistory: string | null | undefined;
  currentPrice: string | number;
  dollerPerUmFormatter: (value: string | number) => string;
  index: number;
  watch: any;
  register: any;
  control: any;
  setValue: any;
  getValues: any;
  pricePerUnitChangeHandler: any;
  setIsCreatePoDirty: any;
  saveUserActivity: any;
  actualIndex: number;
  priceUnits: any;
}

const PriceHistoryDropdown: React.FC<PriceHistoryDropdownProps> = ({
  lineHistory,
  currentPrice,
  dollerPerUmFormatter,
  index,
  watch,
  register,
  control,
  setValue,
  getValues,
  pricePerUnitChangeHandler,
  setIsCreatePoDirty,
  saveUserActivity,
  actualIndex,
  priceUnits
}) => {
  // Internal state management
  const [showPriceHistory, setShowPriceHistory] = useState(false);
  const [priceHistoryData, setPriceHistoryData] = useState<PriceHistoryItem[]>([]);
  const [priceChangeIndicator, setPriceChangeIndicator] = useState<PriceChangeIndicator>({
    show: false,
    direction: null,
    color: '',
    changeAmount: 0
  });
  const anchorRef = useRef<HTMLDivElement>(null);

  // Parse price history from line_history
  useEffect(() => {
    if (lineHistory) {
      try {
        // const historyData = JSON.parse(lineHistory);
        const historyData = [
    {
        "created_date": "Original",
        "qty": "20",
        "qty_unit": "PC",
        "buyer_price_per_unit": 42.78,
        "price_unit": "CWT",
        "buyer_line_total": 72
    },
    {
        "created_date": "6/20/2025",
        "qty": "23",
        "qty_unit": "PC",
        "buyer_price_per_unit": 43.78,
        "price_unit": "CWT",
        "buyer_line_total": 73
    },
    {
        "created_date": "6/21/2025",
        "qty": "23",
        "qty_unit": "PC",
        "buyer_price_per_unit": 44.78,
        "price_unit": "CWT",
        "buyer_line_total": 73
    },
    {
        "created_date": "6/22/2025",
        "qty": "23",
        "qty_unit": "PC",
        "buyer_price_per_unit": 45.78,
        "price_unit": "CWT",
        "buyer_line_total": 73
    }
];
        if (Array.isArray(historyData)) {
          setPriceHistoryData(historyData);
          
          // Calculate price change indicator
          if (historyData.length > 0 && currentPrice) {
            // Get the original price (first item in history)
            const originalPriceItem = historyData[0];
            
            if (originalPriceItem && originalPriceItem.buyer_price_per_unit) {
              const currentPriceNum = parseFloat(String(currentPrice));
              const originalPriceNum = parseFloat(String(originalPriceItem.buyer_price_per_unit));
              
              if (true) {
                const priceDiff = currentPriceNum - originalPriceNum;
                setPriceChangeIndicator({
                  show: true,
                  direction:  <RedArrow/> ,
                  color: priceDiff > 0 ? '#ff6b6b' : '#51cf66',
                  changeAmount: 45
                });
              } else {
                setPriceChangeIndicator({ show: false, direction: '', color: '', changeAmount: 0 });
              }
            } else {
              setPriceChangeIndicator({ show: false, direction: '', color: '', changeAmount: 0 });
            }
          } else {
            setPriceChangeIndicator({ show: false, direction: '', color: '', changeAmount: 0 });
          }
        }
      } catch (error) {
        console.error('Error parsing line_history:', error);
        setPriceHistoryData([]);
        setPriceChangeIndicator({ show: false, direction: '', color: '', changeAmount: 0 });
      }
    } else {
      setPriceHistoryData([]);
      setPriceChangeIndicator({ show: false, direction: '', color: '', changeAmount: 0 });
    }
  }, [lineHistory, currentPrice]);
  return (
    <>
      <div className={styles.pricePerUnitContainer}>
        {/* Price Change Indicator */}
        <div
          ref={anchorRef}
          className={clsx(styles.priceChangeIndicator, showPriceHistory && styles.priceChangeIndicatorShow)}
          onMouseEnter={() => {
            if (priceHistoryData.length > 0) {
              setShowPriceHistory(true);
            }
          }}
          onMouseLeave={() => {
            setShowPriceHistory(false);
          }}
          onClick={() => {
            if (priceHistoryData.length > 0) {
              setShowPriceHistory(!showPriceHistory);
            }
          }}
        >
          {true && (
          <span
            className={styles.priceChangeArrow}
            style={{ color: priceChangeIndicator.color }}
          >
            {priceChangeIndicator.direction}
          </span>
        )}

          <span>{!!currentPrice && dollerPerUmFormatter(currentPrice)}</span>
          
      {(watch(`cart_items.${index}.price_unit`) && watch('isEdit')) ?
        <span className={styles.selectUom1}>
          <CustomMenu
            name={register(`cart_items.${index}.price_unit`).name}
            control={control}
            // disabled={apiCallInProgress}
            onChange={() => {
              pricePerUnitChangeHandler(index);
              setIsCreatePoDirty(true);
              saveUserActivity();
            }}
            items={
              getValues(`cart_items.${index}.price_um`)?.map((x) => ({ title: (x.toLowerCase() === priceUnits.pc) ? 'PC' : x, value: x.toLowerCase() })) ?? []
            }
            className={clsx(styles.uomDrodown, 'qtyUnitDropdown')}
            MenuProps={{
              classes: {
                paper: styles.selectUomPaper,
              },
              id: `price-unit-menu-${actualIndex}` // Add index to make ID unique
            }}
            onKeyDown={(e) => {
              if (e.key === 'Tab' && document.activeElement?.closest(`#price-unit-menu-${actualIndex}`)) { // Update selector to match new ID
                const value = document.activeElement.getAttribute('data-value');
                setValue(`cart_items.${index}.price_unit`, value);
                pricePerUnitChangeHandler(index);
                setIsCreatePoDirty(true);
                saveUserActivity();
              }
            }}
          />
        </span>
        :
        <span className={styles.selectUom}>
          <span className={styles.selectUomValue}>
            {watch(`cart_items.${index}.price_unit`)}
          </span>
        </span>
      }
        </div>
      
        
        {/* Price History Popover */}
        <Popover
                  open={showPriceHistory && priceHistoryData.length > 0}

          anchorEl={anchorRef.current}
          onClose={() => setShowPriceHistory(false)}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
          disableRestoreFocus
          sx={{
            pointerEvents: 'none',
          }}
          classes={{
            paper: styles.priceHistoryDropdown,
          }}
          slotProps={{
            paper: {
              onMouseEnter: (set) => setShowPriceHistory(true),
              onMouseLeave: () => setShowPriceHistory(false),
            },
          }}
        >
          <div className={styles.priceHistoryDropdownContent}>
            {priceHistoryData.map((historyItem, historyIndex) => (
              <div key={historyIndex} className={styles.priceHistoryRow}>
                <div className={styles.priceHistoryLabel}>
                  {historyItem.created_date || 'Original'}
                </div>
                <div className={styles.priceHistoryValues}>
                  <span className={styles.priceHistoryQty}>
                    {String(historyItem.qty || '')}
                  </span>
                  <span className={styles.priceHistoryUnit}>
                    {String(historyItem.qty_unit || '')}
                  </span>
                  <span className={clsx(
                    styles.priceHistoryPrice,
                    String(historyItem.buyer_price_per_unit) === formatToTwoDecimalPlaces(String(currentPrice)) && styles.currentPrice
                  )}>
                    {formatToTwoDecimalPlaces(String(historyItem.buyer_price_per_unit || 0))}
                  </span>
                  <span className={styles.priceHistoryUnit}>
                    {String(historyItem.price_unit || '')}
                  </span>
                  <span className={styles.priceHistoryTotal}>
                    {formatToTwoDecimalPlaces(String(historyItem.buyer_line_total || 0))}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </Popover>
      </div>

    </>
  );
};

export default PriceHistoryDropdown; 