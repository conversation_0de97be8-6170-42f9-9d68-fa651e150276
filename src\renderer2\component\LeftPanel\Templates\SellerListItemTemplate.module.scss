.sellerListItemCard {
  padding: 12px 12px 29px 12px;
}

.headerRow {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  position: relative;
}

.itemDescription {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.98px;
  text-align: left;
  color: #fff;
}

.price {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.98px;
  text-align: left;
  color: #fff;
}

.actionIcons {
  display: flex;
  gap: 8px;
  align-items: center;
  position: absolute;
  top: 24px;
  right: 0;
}

.iconButton {
  &:hover {
    .deleteIcon {
      path{
        fill: #fff;
      }
  }
  .shareIcon {
    path{
      fill: #fff;
    }
  }
  .resetIcon {
    path{
      fill: #fff;
    }
  }
  }
}

.detailsContainer {
  display: flex;
  flex-direction: column;
  gap: 1px;
  font-family: Inter;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.84px;
  text-align: left;
  color: #9b9eac;
}

.detailRow {
  display: flex;
  align-items: center;
}
.detailRow1 {
  display: flex;
  align-items: center;
  position: absolute;
  top: 21px;
  font-family: Inter;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.84px;
  text-align: left;
  color: #9b9eac;
  .countdownTimer {
    color: rgba(50, 255, 108, 0.61);
  }
}

.location,
.weight,
.deliveryDate {
  font-size: 13px;
  color: #b0b0b0;
  line-height: 1.3;
  font-weight: 400;
}

.selectedOrder {
  background-color: #434449;

  .detailsContainer {
    color: #fff;
  }

  .iconButton {
    .deleteIcon {
      path {
        fill: #fff;
      }
    }

    .shareIcon {
      path {
        fill: #fff;
      }
    }
    .resetIcon {
      path {
        fill: #fff;
      }
    }
  }
  .detailRow1 {
    color: #fff;
  }
}