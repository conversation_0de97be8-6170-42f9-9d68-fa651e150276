.navTab{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    &:hover{
        overflow: visible;
    }
    .routingPanel{
        flex: 1;
        display: flex;
        flex-direction: column;
        padding-top: 12px;
    }
    .logout{
        height: 3.63%;
        .logoutButton{
            font-family: Inter;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.15;
            letter-spacing: normal;
            text-align: left;
            color: #71737f;
            padding: 8px;
            width: 100%;
            text-align: center;
        }
    }
    .sideBarButton{
        font-family: Syncopate;
        font-size: 24px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.2;
        letter-spacing: 1.68px;
        text-align: center;
        color: #9b9eac;
        // padding: 14.29% 14.29% 0% 14.29%;
        // aspect-ratio: 1.17;
        // height: 5.32%;
        height: 48px;
        width: 100%;
        position: relative;
        z-index: 2;
        
    }
    .sideBarButtonActive {
        &.pricing {
            color: #32ff6c;
            .instantPriceIcon1{
                display: none;
            }
            .instantPriceIcon2{
                display: block;
            }
        }
        &.quoting {
            color: #32ccff;
        }
        &.purchasing {
            color: #ffe352;
        }
        &.order {  
            color: #ff8c4c;
        }
    }
    .routeOptions{
        &:hover{
            .routeBtnHover, .optionHoverBg{
                // width: 400%;
                opacity: 1;
            }
        }
    }
    .routeOptions{
        position: absolute;
        // margin-left: calc(50% - 0.5rem);
        inset: 0;
        z-index: 1;
        display: flex;
        align-items: baseline;
        opacity: 0;
        transition: opacity 1s ease;
        .optionHoverBg2{
            position: absolute;
            z-index: -1;
            // left: 8px !important;
            // bottom: 4px;
            // top: 4px !important;
            box-shadow: 9px 4px 18.6px 0 #000;
            background-color: #191a20;
            border-radius: 2rem;
            // transition: opacity 1s ease;
            inset: -8px -12px;
            
        }
        .pricingPosition{
            inset: 0 !important;
        }
        .animationEffect{
            transition: opacity 0.75s ease;
            font-size: 16px;
            font-family: Syncopate;

        }

    }
    .pricingHover{
        width: 350px;
        align-items: center !important;
    }
    .quotingHover{
        width: 125px;
    }
    .purchasingHover{
        width: 170px;
    }
    .orderHover{
        width: 276px;
    }
    
    .pricing:hover{
        color: #32ff6c;
        .instantPriceIcon1{
            display: none;
        }
        .instantPriceIcon2{
            display: block;
        }
    }
    .quoting:hover{
        color: #32ccff;
    }
    .purchasing:hover{
        color: #ffe352;
    }
    .order:hover{
        color: #ff8c4c;
    }
}
.instantPriceIcon{
    display: flex;
    justify-content: center;
    align-items: center;
    .instantPriceIcon1{
        display: block;
    }
    .instantPriceIcon2{
        display: none;
    }
    &:hover{
        .instantPriceIcon1{
            display: none;
        }
        .instantPriceIcon2{
            display: block;
        }
    }
}

.mainButton{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    &:hover{
        .routeOptions{
            opacity: 1;
        }
    }
    .positionRelative{
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }
   
}
.badge {
    background-color: #ff4848;
    width: auto;
    padding: 0px 5px;
    border-radius: 50px;
    display: flex
;
    align-items: center;
    justify-content: center;
    font-family: Inter;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: 0.84px;
    text-align: center;
    color: #fff;
    position: absolute;
    top: 5px;
    right: 10px;
}