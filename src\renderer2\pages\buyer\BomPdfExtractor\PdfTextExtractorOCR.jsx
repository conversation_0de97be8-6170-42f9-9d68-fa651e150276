import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import * as pdfjs from 'pdfjs-dist';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.min?url'
import { ADD_BOX, DELETE_BOX, RESIZE_BOX, ROTATE, useBomPdfExtractorStore, ZOOM } from './BomPdfExtractorStore';
import {searchProducts, getValidSearchData, useCreatePoStore, commomKeys, orderIncrementPrefix, priceUnits, getValUsingUnitKey, getFloatRemainder} from '@bryzos/giss-ui-library';
import { processProductExtraction, createBomUploadWrapper } from './utils/BOMUploadUtils';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import styles from './styles/BomExtractor.module.scss';
//import {ProductMapping, ProductSearch} from './utils/productSearch';
import textractService from './services/textractService';
import { parseSteel } from '@bryzos/steel-search-lib';

import { v4 as uuidv4 } from 'uuid';
import './styles/App.css';
// Import box types configuration
import {
  BOX_TYPES,
  getBoxStyles,
  createEmptyDataArrays,
} from './config/boxTypes';
// Import TextractRBush
import TextractRBush from './textract-rbush';
import PdfjsSpatialIndex from './pdfjsSpatialIndex';
import PdfPage from './components/PdfPage';
import { routes } from 'src/renderer2/common';
import { navigatePage } from 'src/renderer2/helper';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import { useNavigate } from 'react-router-dom';
import OrientationSlider from './components/OrientationSlider';
import useUndoRedoShortcuts from './utils/useUndoRedoShortcuts';



// Setup worker using CDN
pdfjs.GlobalWorkerOptions.workerSrc = pdfjsWorker
// pdfjs.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`

function logBox(...args) {
  //console.log(...args);
}
function logRender(...args) {
  //console.log(...args);
}

function logOcr(...args) {
  //console.log(...args);
}

function logError(...args) {
 // console.error(...args);
}

function logPdf(...args) {
 // console.log(...args);
}

// Props:
// - pdfFile: The PDF file to display
// - onError: Function to handle errors
// - onBoxesChange: Function to notify parent of boxes state
// - onExtractedDataChange: Function to notify parent of extracted data state
// - currentBoxType: The current box type selected in the parent
const PdfTextExtractorOCR = React.forwardRef((props, ref) => {
  const {
    onError,
    onBoxesChange,
    onExtractedDataChange,
    onVisibilityChange, // Optional callback for visibility changes
  } = props;
  const [numPages, setNumPages] = useState(0);
  const [fileName, setFileName] = useState('');
  const [pageNumber, setPageNumber] = useState(1);
  const [pdfPages, setPdfPages] = useState([]);
  const [scale, setScale] = useState(3); // This is the internal scale for high-res rendering
  const [boxes, setBoxes] = useState([]);
  const [isDrawing, setIsDrawing] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [resizeHandle, setResizeHandle] = useState(null); // 'tl', 'tr', 'bl', 'br', 'left', 'right', 'top', 'bottom'
  const [resizingBoxId, setResizingBoxId] = useState(null);
  const [startPoint, setStartPoint] = useState({ x: 0, y: 0 });
  const [currentRect, setCurrentRect] = useState(null);
  const [errorMessage, setErrorMessage] = useState('');
  const [hoveredBox, setHoveredBox] = useState(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const canvasRef = useRef(null);
  
  const hiResCanvasRef = useRef(null); // Hidden high-resolution canvas
  const [magnifyingGlassPosition, setMagnifyingGlassPosition] = useState({ x: 0, y: 0 });

  // Viewport detection refs and state
  const scrollableContainerRef = useRef(null);
  const containerRef = useRef(null);
  const intersectionObserverRef = useRef(null);
  const [visiblePages, setVisiblePages] = useState(new Set());
  const [fullyVisiblePages, setFullyVisiblePages] = useState(new Set());

  // Use the box styles from our configuration
  const boxStyles = getBoxStyles();

  const [extractedData, setExtractedData] = useState({});
  const [allExtractedData, setAllExtractedData] = useState({});
  //const [allBoxes, setAllBoxes] = useState({});
  const [processedBoxIds, setProcessedBoxIds] = useState(new Set());
  const [processingBoxIds, setProcessingBoxIds] = useState(new Set());
  // Note: Using pageRotations instead of a single rotation state
  //const [pageRotations, setPageRotations] = useState({});

  const [rotation, setRotation] = useState(0);

  // Store skew adjustments for each page (-45 to +45 degrees)
  const [fineRotations, setFineRotations] = useState({});
  // Store raw box data with position information for all pages
  const [rawBoxData, setRawBoxData] = useState({});
  // Use a ref to track the latest raw box data for use in setTimeout callbacks
  const rawBoxDataRef = useRef({});

  // State for TextractRBush spatial index
  //const [textractRBush, setTextractRBush] = useState(null);
  const [pdfJSPage, setPdfJSPage] = useState(null);
  const pdfPageRefs = useRef([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [orientation, setOrientation] = useState(0);


  const { extractText, setExtractText, gridOpacity, autoDetectColumns, currentBoxType, setCurrentBoxType,
    overlapPercent, snapToGrid, pdfFile, showMagnifyingGlass, setShowMagnifyingGlass,
  doResetAll, doUndoBox, doExportCSV, setDoResetAll, setDoUndoBox, setDoExportCSV, setPdfjs, setAllBoxes, allBoxes, setSourceCanvasRefs,
  doClearAll, setDoClearAll, zoomPercentage, setZoomPercentage, s3Url, bomUploadID,doNext, setDoNext, setBomData, setHasBoxes,
  isImageBasedPdf, setGeometryData,geometryData, domesticOnly, pageRotations, textractRBushInitialized, setTextractRBushInitialized,
  setScrollViewPort, updatedScrollViewPort, doResetBomPdfExtractorStore, setShowBackToBomUploadButton, textractRBush, setTextractRBush, 
  doAutoScroll, setdoAutoScroll, customBoxTypes, setPdfUrl, setPdfFileName, pdfFilename, productSearcher, undoStack, setUndoStack, redoStack, setRedoStack,
  setColumnDef, canvasRotation, setCanvasRotation, setGridRowData, doUndo, doRedo, setDoUndo, setDoRedo, finalExtractedData, 
  doSubmit, setDoSubmit, setDoBack, doBack, setPageRotations, 
  } = useBomPdfExtractorStore();




  const {showCommonDialog, resetDialogStore} = useDialogStore();

  const {setIsCreatePOModule, uploadBomInitialData} = useCreatePoStore();

  const {productData} = useGlobalStore();
  const navigate = useNavigate();


  // Keep the ref in sync with the state
  useEffect(() => {
    rawBoxDataRef.current = rawBoxData;
  }, [rawBoxData]);


  // Add a state variable to track PDF rendering
  const [pdfRenderKey, setPdfRenderKey] = useState(0);


  useEffect(()=>{
    if(allBoxes){
      let checkIfHasBoxes = false;
      allBoxes.forEach(pageBoxes=>{
        if(pageBoxes && pageBoxes.length>0){
          checkIfHasBoxes = true;
          return;
        } 
      }); 
      setHasBoxes(checkIfHasBoxes);
    }
  },[allBoxes]);

  useEffect(()=>{

    if(pdfFile){
      initializePdf();
    }
  },[pdfFile] );

  useEffect(()=>{
    setShowBackToBomUploadButton(false);
    localIsProcessing = false;
    setIsProcessing(false);
    
  },[]);

  useEffect(()=>{
    const allBoxesList = allBoxes.reduce((acc, curr) => acc.concat(curr), []);
    const seen = new Set();
    const uniqueBoxes = allBoxesList.filter(item => {
      if (!item || seen.has(item.type)) return false;
      seen.add(item.type);
      return true;
    });
    const allColumnDefs = uniqueBoxes.map(box => BOX_TYPES[box.type].columnDef);
    setColumnDef(allColumnDefs);
  }, [allBoxes]);


  const onPdfPageMounted = (index) => {
    if(allBoxes[index])
      pdfPageRefs.current[index].setPageBoxes(allBoxes[index]);
  }

  const executeAction = (action, state)=>{
    switch(action){
      case RESIZE_BOX:        
        setAllBoxes(prev=>{
          let changeIndex = -1;
          prev[state.pageIndex].forEach((box, index)=>{
            if(box.id === state.id){
              prev[state.pageIndex][index] = {...prev[state.pageIndex][index], rect:{...state.rect}};
            }
          });
          return prev;
        });

        if(pdfPageRefs.current[state.pageIndex]) {
          pdfPageRefs.current[state.pageIndex].resizeBox(state);
        }
        break;        
      case ZOOM:
        setZoomPercentage(state.zoomPercentage);
        break;
      case ADD_BOX:
        setAllBoxes(prev=>{
          prev[state.pageIndex].push(state);
          return prev;
        });
        if(pdfPageRefs.current[state.pageIndex]) {
          pdfPageRefs.current[state.pageIndex].addBox(state);
        }
        break;
      case DELETE_BOX:
        setAllBoxes(prev=>{
          prev[state.pageIndex] = prev[state.pageIndex].filter(box => box.id !== state.id);
          return prev;
        });
        if(pdfPageRefs.current[state.pageIndex]) {
          pdfPageRefs.current[state.pageIndex].deleteBox(state);
        }
        break;
      case ROTATE:
        setPageRotations(state.pageRotations);
        setCanvasRotation(state.canvasRotation);
        break;
    }
  }

  useEffect(() => {
    if (doUndo) {
      handleUndo();
      setDoUndo(false);
    }
  }, [doUndo]);

  useEffect(() => {
    if (doRedo) {
      handleRedo();
      setDoRedo(false);
    }
  }, [doRedo]);

  useEffect(() => {
    if (doBack) {
      navigateBack();
      setDoBack(false);
    }
  },[doBack]);

  const navigateBack = async () => {
    try{
      const bomData = await textractService.getBomData(bomUploadID);
      console.log(bomData);
      if(bomData.data){
        navigateAway({ state: { from: 'bomPdfExtractor' } }, bomData.data);
      }else{
        const {fileName, s3Url, bomUploadID, domesticOnly} = useBomPdfExtractorStore.getState();
        console.log(uploadBomInitialData);
        const bomContext = {
          fileName,
          s3Url,
          bomUploadID,
          numPages,
          domesticOnly,
          uploadBomInitialData
        };
        const wrapperObj = createBomUploadWrapper([], bomContext);

        wrapperObj.file_name = pdfFile.name;
        wrapperObj.actual_file_name = pdfFile.name;
        
        navigateAway({ state: { from: 'bomPdfExtractor' } }, wrapperObj);
      }
    }
    catch(error){
      console.log(error);
    }
  }
  


  const handleUndo = useCallback(() => {
    const { undoStack } = useBomPdfExtractorStore.getState();
    if(undoStack.length === 0) return;
    const action = undoStack[undoStack.length - 1];
    if(action){
      executeAction(action.undoAction.action, action.undoAction.state);
      setRedoStack(prev=>[...prev, action]);
    }
    setUndoStack(prev=>prev.slice(0, -1));
  }, []);

  const handleRedo = useCallback(() => {
    const { redoStack } = useBomPdfExtractorStore.getState();
    if(redoStack.length === 0) return;
    const action = redoStack[redoStack.length - 1];
    if(action){
      executeAction(action.redoAction.action, action.redoAction.state);
      setUndoStack(prev=>[...prev, action]);
    }
    setRedoStack(prev=>prev.slice(0, -1));
  }, []);

  useUndoRedoShortcuts(handleUndo, handleRedo);

  useEffect(() => {
    const setupIntersectionObserver = () => {
      // Clean up existing observer
      if (intersectionObserverRef.current) {
        intersectionObserverRef.current.disconnect();
      }

      // Create new observer with options
      const observerOptions = {
        root: scrollableContainerRef.current,
        rootMargin: '50px', // Start observing 50px before entering viewport
        threshold: [0, 0.1, 0.5, 0.9, 1.0] // Multiple thresholds for granular detection
      };

      const handleIntersection = (entries) => {
        entries.forEach((entry) => {
          const pageIndex = parseInt(entry.target.dataset.pageIndex);

          if (entry.isIntersecting) {
            // Page is at least partially visible
            setVisiblePages(prev => new Set([...prev, pageIndex]));

            // Check if page is fully visible (90% threshold)
            if (entry.intersectionRatio >= 0.9) {
              setFullyVisiblePages(prev => new Set([...prev, pageIndex]));
            } else {
              setFullyVisiblePages(prev => {
                const newSet = new Set(prev);
                newSet.delete(pageIndex);
                return newSet;
              });
            }
          } else {
            // Page is not visible
            setVisiblePages(prev => {
              const newSet = new Set(prev);
              newSet.delete(pageIndex);
              return newSet;
            });
            setFullyVisiblePages(prev => {
              const newSet = new Set(prev);
              newSet.delete(pageIndex);
              return newSet;
            });
          }
        });
      };

      intersectionObserverRef.current = new IntersectionObserver(handleIntersection, observerOptions);
    };

    // Setup observer when PDF pages are available
    if (pdfPages.length > 0) {
      setupIntersectionObserver();
    }

    // Cleanup on unmount
    return () => {
      if (intersectionObserverRef.current) {
        intersectionObserverRef.current.disconnect();
      }
    };
  }, [pdfPages.length]); // Re-setup when number of pages changes

  // Observe page containers when they're rendered
  useEffect(() => {
    if (intersectionObserverRef.current && pdfPages.length > 0) {
      // Observe all page containers
      const pageContainers = scrollableContainerRef.current?.querySelectorAll('[data-page-index]');
      pageContainers?.forEach((container) => {
        intersectionObserverRef.current.observe(container);
      });

      // Cleanup function to unobserve when pages change
      return () => {
        if (intersectionObserverRef.current) {
          pageContainers?.forEach((container) => {
            intersectionObserverRef.current.unobserve(container);
          });
        }
      };
    }
  }, [pdfPages, intersectionObserverRef.current]); // Re-observe when pages or observer changes

  // Debug effect to log visibility changes and notify parent
  useEffect(() => {
    const visiblePagesArray = Array.from(visiblePages).sort((a, b) => a - b);
    const fullyVisiblePagesArray = Array.from(fullyVisiblePages).sort((a, b) => a - b);

    // Notify parent component if callback is provided
    if (onVisibilityChange) {
      const visibilityInfo = {
        visiblePages: visiblePagesArray,
        fullyVisiblePages: fullyVisiblePagesArray,
        totalPages: pdfPages.length,
        visibleCount: visiblePages.size,
        fullyVisibleCount: fullyVisiblePages.size
      };
      onVisibilityChange(visibilityInfo);
    }
  }, [visiblePages, fullyVisiblePages, pdfPages.length, onVisibilityChange]);

  const initializePdf = async () => {
    if (!pdfFile) return;

    try {
      setFileName(pdfFile.name);
      const arrayBuffer = await pdfFile.arrayBuffer();
      const pdf = await pdfjs.getDocument({ data: arrayBuffer }).promise;
      if(!isImageBasedPdf ){
        const pdfjsSpatialIndex = new PdfjsSpatialIndex();
        await pdfjsSpatialIndex.initialize(pdf);
        setTextractRBush(pdfjsSpatialIndex);
      }

      setPdfjs(pdf);
      setNumPages(pdf.numPages);
      const pages = [];
      const tempArr = [];
      for(let i = 0; i < pdf.numPages; i++){
        const page = await pdf.getPage(i+1);
        pages.push(page);
        tempArr.push([]);
        //setPdfPages(prev=>([...prev, page]));
      }
      //setAllBoxes(tempArr);
      setPdfPages(pages);
      setSourceCanvasRefs(new Array(pdf.numPages).fill(null));
    } catch (error) {
      console.error('Failed to initialize PDF', error);
    }
  };

  const [displayArrays, setDisplayArrays] = useState(null);

  useEffect(() => {
    if (extractText) {
      extractImages();
    }
  }, [extractText]);

  useEffect(() => {
    if (doResetAll) {
      if(pdfPageRefs.current.length === 0) return;
      pdfPageRefs.current.forEach(ref => ref?.clearBoxes());
      setDoResetAll(false);
    }
    //This is a hack. After all the pages boxes have been cleared reset allBoxes. 
    setTimeout(()=>{
      const all_boxes = pdfPageRefs.current.map(ref => ref?.getBoxes() || []);
      setAllBoxes(all_boxes);
      setHasBoxes(false);
    },300)
  }, [doResetAll]);

  useEffect(() => {
    if (doClearAll) {
      if(pdfPageRefs.current.length>0){
        pdfPageRefs.current.forEach(page => {
          if(page){
            page.clearBoxes();
          }
        });
      }
      setDoClearAll(false);
    }
  }, [doClearAll]);

  // useEffect(() => {
  //   if (doUndoBox) {
  //     const undoBoxesStack = [];
  //     allBoxes.forEach((pageBoxes, index)=>{
  //       if(pageBoxes)
  //         pageBoxes.forEach(box=>{
  //           undoBoxesStack.push({index:index, box:box});
  //         })
  //     });
  //     undoBoxesStack.sort((a, b) => b.box.timeStamp - a.box.timeStamp);
  //     if(undoBoxesStack.length > 0){
  //       pdfPageRefs.current[undoBoxesStack[0].index].undoBox(undoBoxesStack[0].box);
  //     }
  //     setDoUndoBox(false);
  //   }
  // }, [doUndoBox]);

  useEffect(() => {
    if (doExportCSV) {
      setDoExportCSV(false);
    }
  }, [doExportCSV]);

  useEffect(() => {
    if (doNext) {
      extractImages();
      setDoNext(false);
    }
  }, [textractRBush]);

  useEffect(() => {
    if (doNext && textractRBush) {
      extractImages();
      setDoNext(false);
    }
  }, [doNext]);

  // Extract images from boxes
  const extractImages = async () => {
    setExtractText(false);

    logOcr('Starting OCR extraction process', {
      pdfFilename: pdfFile?.name,
      pageNumber,
      totalBoxes: boxes.length,
      processedBoxes: processedBoxIds.size,
      scale,
      rotation: pageRotations[`page${pageNumber}`] || 0,
      fineRotation: fineRotations[`page${pageNumber}`] || 0,
    });
    const startTime = performance.now();


    if (!pdfFile ||  !pdfPageRefs.current.length === 0) {
      const reason = !pdfFile
        ? 'No PDF loaded'
        :  'Canvas not available';
      logError(`OCR extraction failed: ${reason}`, {
        pdfFilename: pdfFile?.name,
        boxCount: boxes.length,
        canvasAvailable: !!canvasRef.current,
      });
      return;
    }

    // Check if TextractRBush is initialized
    
    if (textractRBush) {
      //processBoxes(boxes);
      const currentPageKey = `page${pageNumber}`;
      const tempBoxes = {};
      tempBoxes[currentPageKey] = boxes;

      logBox(`Saving boxes for page ${pageNumber}`, {
        pageNumber,
        boxCount: boxes.length,
        boxTypes: boxes.reduce((acc, box) => {
          acc[box.type] = (acc[box.type] || 0) + 1;
          return acc;
        }, {}),
      });
      // let all_boxes = {...allBoxes, ...tempBoxes };
      // setAllBoxes((prev) => {
      //   return {...prev, ...tempBoxes };
      // })
      const all_boxes = pdfPageRefs.current.map(ref => ref?.getBoxes() || []);
      processAllBoxes(all_boxes);

    }
    else{
      setProcessWhenReady(true)
    }
  };
  let localIsProcessing = false;

  const [processWhenReady, setProcessWhenReady] = useState(false);

  const processAllBoxes = (all_boxes) => {
    if(localIsProcessing||isProcessing) return;
    localIsProcessing = true;
    setIsProcessing(true);
    setGeometryData(all_boxes)
    saveGeometryData(all_boxes);
    //for(let pageID in all_boxes){
    all_boxes.forEach((boxes, index)=>{
      const pageNumber = index+1//pageID.slice(4);
      const pageID = `page${pageNumber}`;

      // Get canvas dimensions for this page
      const canvasSize = pdfPageRefs.current[pageNumber-1]?.getCanvasSize() || { width: 0, height: 0 };

      // Convert normalized boxes to canvas coordinates
      // const canvasBoxes = boxes.map(box => ({
      //   ...box,
      //   rect: {
      //     x: box.rect.x * canvasSize.width,
      //     y: box.rect.y * canvasSize.height,
      //     width: box.rect.width * canvasSize.width,
      //     height: box.rect.height * canvasSize.height
      //   }
      // }));


      //const pageData = processBoxes(canvasBoxes, Number(pageNumber));
      const pageData = processBoxes(boxes, Number(pageNumber));
      if(Object.keys(pageData.displayArrays)?.length>0){
        setDisplayArrays(prev=>({...prev, [pageID]: {
          pageNumber,
          rowCount:pageData.displayArrays[Object.keys(pageData.displayArrays)[0]].length,
          texts:pageData.displayArrays}}));
        //setRawText(prev=>({...prev, [pageNumber]: pageData.rowText}));
      }else{
        localIsProcessing = false;
        setIsProcessing(false);
      }
    })
  }
  function objectOfArraysToArrayOfObjects(obj) {
    const keys = Object.keys(obj);
    const length = obj[keys[0]].length;
    return Array.from({ length }).map((_, i) =>
      keys.reduce((acc, key) => {
        acc[key] = obj[key][i];
        return acc;
      }, {})
    );
  }


  

  const saveGeometryData = (allBoxes)=>{
    if(!allBoxes || !bomUploadID) return;
    const geometryPayload = {
        "bom_upload_id": bomUploadID,
        "selection_data":  {geometryData: allBoxes, customBoxTypes:customBoxTypes}
    }
    textractService.saveGeometryData(geometryPayload);
  }



  useEffect(()=>{
    if(!displayArrays) return;

    const processData = async () => {
      const extractedData = [];
      const confidanceRange = [
        {
          min_match_count: 2,
          max_match_count: 19,
          confidence:50
        },
        {
          min_match_count: 20,
          max_match_count: null,
          confidence:0
        }
      ];

      const filteredProductData = productData.filter((product)=>!product.is_safe_product_code)

      Object.keys(displayArrays).forEach((pageID)=>{
        objectOfArraysToArrayOfObjects(displayArrays[pageID].texts).forEach(obj=>{
          extractedData.push({...obj, pageNumber: displayArrays[pageID].pageNumber});
        });
      })

      // Use the new processProductExtraction function
      const bomContext = {
        fileName,
        s3Url,
        bomUploadID,
        numPages,
        domesticOnly,
        uploadBomInitialData
      };

      try {
        const result = await processProductExtraction(
          filteredProductData,
          extractedData,
          confidanceRange,
          bomContext,
          productSearcher
        );

        if (result.success && result.hasData) {
          setGridRowData(result.gridData);
          localIsProcessing = false;
          setIsProcessing(false);
        } else if (result.success && !result.hasData) {
          showCommonDialog(null, "No data was extracted, please try adjusting the boxes.", null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
          localIsProcessing = false;
          setIsProcessing(false);
        } else {
          // Handle error case
          showCommonDialog(null, `Error: ${result.error}`, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
          localIsProcessing = false;
          setIsProcessing(false);
        }
      } catch (error) {
        console.error('Error processing product extraction:', error);
        showCommonDialog(null, "An unexpected error occurred during processing.", null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
        localIsProcessing = false;
        setIsProcessing(false);
      }
    };

    processData();
    localIsProcessing = false;
    setIsProcessing(false);

  },[displayArrays]);

  useEffect(()=>{
    //return if already is processing
    if(localIsProcessing || isProcessing)return;
    if(!doSubmit) return;

    localIsProcessing = true;
    setIsProcessing(true);
    setDoSubmit(false);

    const processDataAndSubmit = async ()=>{
      const filteredProductData = productData.filter((product)=>!product.is_safe_product_code);
      const confidanceRange = [
        {
          min_match_count: 2,
          max_match_count: 19,
          confidence:50
        },
        {
          min_match_count: 20,
          max_match_count: null,
          confidence:0
        }
      ];

      const bomContext = {
        fileName,
        s3Url,
        bomUploadID,
        numPages,
        domesticOnly,
        uploadBomInitialData
      };

       try{
        const result = await processProductExtraction(
            filteredProductData,
            finalExtractedData,
            confidanceRange,
            bomContext,
            productSearcher
          );

          if (result.success && result.hasData) {
            // Create wrapper only when needed (e.g., for logging or API calls)
            const wrapperObj = createBomUploadWrapper(result.results, bomContext);

            await textractService.saveExtractedData(wrapperObj);
            const bomData = await textractService.getBomData(bomUploadID);
            //navigateAway(routes.bomUploadReview, { state: { from: 'bomPdfExtractor' } }, bomData.data);
            navigateAway({ state: { from: 'bomPdfExtractor' } }, bomData.data);
          } else {
            // Handle error case
            showCommonDialog(null, `Something went wrong. Please try again.`, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
            localIsProcessing = false;
            setIsProcessing(false);
          }
        }catch(error){
          console.error('Error processing product extraction:', error);
          showCommonDialog(null, "An unexpected error occurred during processing.", null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
          localIsProcessing = false;
          setIsProcessing(false);
        }
    }

    processDataAndSubmit();
    
  },[doSubmit]);

  const navigateAway = ( navigationOptions, bomData) => {
    doResetBomPdfExtractorStore.value = false;
    setPdfUrl(bomData.s3_url);
    setPdfFileName(bomData.actual_file_name);
    setBomData(bomData);
    setShowBackToBomUploadButton(true);
    setIsCreatePOModule(false);
    const path = uploadBomInitialData?.order_type === 'QUOTE' ? routes.quotePage : routes.createPoPage;
    navigate(path, navigationOptions);
  }

  function parseLength(desc) {
    const lenPart = desc.split(/x/i).pop().trim();
    const token = lenPart.split(/\s+/)[0];
    const cleaned = token.replace(/ft|in|'|"/gi, '');
    const [feetStr = '', inchStr = ''] = cleaned.split('-');
    const feet = parseInt(feetStr, 10) || 0;
    let inches = 0;
    if (inchStr) {
      if (inchStr.includes('/')) {
        const [num, den] = inchStr.split('/').map(Number);
        inches = num/den;
      } else {
        inches = parseInt(inchStr, 10) || 0;
      }
    }
    return { feet, inches, totalFeet: feet + inches/12 };
  }

  const getSplitString = (splitObj)=>{
    return [
      splitObj?.shape || '',
      splitObj?.category || '',
      splitObj?.dimensions || '',
      splitObj?.spec || '',
      splitObj?.grade || '',
      splitObj?.length || ''
    ].filter(Boolean).join(' ');
  }

  /*
    {
    "Shape_ID": 300,
    "Product_ID": 282030,
    "LBS_FT": "54",
    "Key1": "W10X54",
    "Key2": "Beam",
    "Key3": "Wide",
    "Key4": "Flange",
    "Key5": "W",
    "Key6": "10X54",
    "Key7": null,
    "Key8": "10\"",
    "Key9": "10.1",
    "Key10": "54",
    "Key11": null,
    "Key12": null,
    "Key13": "x",
    "Key14": null,
    "Key15": null,
    "Key16": null,
    "Key17": null,
    "Key18": "A992",
    "Key19": null,
    "Key20": "",
    "Key21": null,
    "Key22": null,
    "Key23": null,
    "Key24": null,
    "Key25": null,
    "Key26": null,
    "Key27": null,
    "Key28": null,
    "Key29": null,
    "Key30": null,
    "Key31": null,
    "Key32": null,
    "Key33": null,
    "Key34": null,
    "Key35": null,
    "UI_Description": "Beam Wide Flange (W) - CS\n10\" x 54 LB x 20ft\nA992\n(1080 lbs/ea)",
    "QUM_Dropdown_Options": "pc,lb,ft",
    "PUM_Dropdown_Options": "cwt,pc,lb,ft",
    "domestic_material_only": 1,
    "Size_Group_ID": "28200",
    "Bucket": 6,
    "Neutral_Pricing_Ft": "39.77",
    "Neutral_Pricing_Ea": "795.31",
    "Neutral_Pricing_LB": "0.7364",
    "Neutral_Pricing_CWT": "73.64",
    "Neutral_Pricing_Net_Ton": "1472.8",
    "Buyer_Pricing_Ft": "37.78",
    "Buyer_Ea": "755.57",
    "Buyer_Pricing_LB": "0.6996",
    "Buyer_Pricing_CWT": "69.96",
    "Buyer_Pricing_Net_Ton": "1399.2",
    "Seller_Pricing_Ft": "39.77",
    "Seller_Pricing_Ea": "795.31",
    "Seller_Pricing_LB": "0.7364",
    "Seller_Pricing_CWT": "73.64",
    "Seller_Pricing_Net_Ton": "1472.8",
    "is_safe_product_code": 0,
    "order_increment_ft": "20",
    "order_increment_pc": "1",
    "order_increment_lb": "1080",
    "order_increment_cwt": "10.8",
    "order_increment_net_ton": "0.54",
    "parsedSteel": {
        "originalText": "Beam Wide Flange (W) - CS 10\" x 54 LB x 20ft A992",
        "shape": "BEAM",
        "dims": "10 X 54",
        "grade": "A992",
        "length": 240,
        "lineCount": 4,
        "linesUsed": 3
    }
}

{
    "shape": "W",
    "grade": "A992",
    "dims": "10 X 54",
    "length": 240
}
  */
  
  const checkIfExactMatch = (splitObj, productDetails) =>{
    if(!splitObj.shape || !splitObj.dims || !splitObj.grade || !splitObj.length) return false;
    const grade = splitObj.grade.replace(/\s+/g, '');
    const dims = splitObj.dims.replace(/\s+/g, '');
    const length = splitObj.length;
    const shape = splitObj.shape.trim();
    const productLength = Number(productDetails.order_increment_ft)*12;
    let isDimenstionsMatch = dims.toLowerCase() === productDetails.parsedSteel.dims.toLowerCase();
    let isShapeMatch = shape.toLowerCase() === productDetails.parsedSteel.shape.toLowerCase();
    let isGradeMatch = grade.toLowerCase() === productDetails.parsedSteel.grade.toLowerCase();
    let isLengthMatch = Math.abs(length - productLength) < 0.001;
    
    for (const key in productDetails){
      if(key.startsWith('Key')&&productDetails[key]){
        if(productDetails[key].toLowerCase() === dims.toLowerCase()){
          isDimenstionsMatch = true;
        }
        if(productDetails[key].toLowerCase() === shape.toLowerCase()){
          isShapeMatch = true;
        }
        if(productDetails[key].toLowerCase() === grade.toLowerCase()){
          isGradeMatch = true;
        }
      }
    }

    return isDimenstionsMatch && isShapeMatch && isGradeMatch && isLengthMatch;
  }





  // --- CSV Export Logic ---
  const exportCSV = () => {
   const keys   = Object.keys(BOX_TYPES);                      // ['description','part_number',…]
   const labels = keys.map(id => BOX_TYPES[id].label);         // ['Description','Part Number',…]

    // 1) Build header row with "Page" first
    const headerRow = ['Page', ...labels].join(',');

    // 2) Flatten all pages & rows into CSV lines
    const csvRows = [headerRow];
    for (let page = 1; page <= numPages; page++) {
      const pageKey  = `page${page}`;
      const pageData = displayArrays[pageKey];
      const rowCount = pageData?.rowCount ?? 0;

      for (let i = 0; i < rowCount; i++) {
        // start with page number
        const row = [page];
        // then each column's text (or blank)
        keys.forEach(id => {
          const txt = pageData?.texts?.[id]?.[i] ?? '';
          // escape any internal quotes
          row.push(`"${txt.replace(/"/g,'""')}"`);
        });
        csvRows.push(row.join(','));
      }
    }

    // 3) Create a blob and trigger download
    const blob = new Blob([csvRows.join('\n')], { type: 'text/csv;charset=utf-8;' });
    const url  = URL.createObjectURL(blob);
    const a    = document.createElement('a');
    a.href     = url;
    a.download = 'table_export.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const drawAllPAges = () => {
    const headers = Object.keys(BOX_TYPES); // preserves insertion order
    return (
      <table className={styles.table} border="1" cellPadding="4" cellSpacing="0">
        <thead>
          <tr>
            <th>No.</th>
            {headers.map((id) => (
              <th key={id}>{BOX_TYPES[id].label}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {Array.from({ length: numPages }, (_, idx) => {
            const pageNumber = idx + 1;
            const pageKey = `page${pageNumber}`;
            const pageData = displayArrays[pageKey];
            const rowCount = pageData ? pageData.rowCount : 1;

            return (
              <React.Fragment key={pageKey}>
                <tr>
                  <td colSpan={headers.length}>
                    Page {pageNumber}
                  </td>
                </tr>

                {Array.from({ length: rowCount }, (_, rowIdx) => (
                  <tr key={`${pageKey}-row-${rowIdx}`}>
                    <td>{rowIdx + 1}</td>
                    {headers.map((id) => {
                      const value = pageData?.texts?.[id]?.[rowIdx] ?? '';
                      return <td key={id}>{value}</td>;
                    })}
                  </tr>
                ))}
              </React.Fragment>
            );
          })}
        </tbody>
      </table>
    );
  };

  
  function parseQuantity(input) {
    const pattern = /^\s*(\d{1,3}(?:,\d{3})*|\d+)(?:\s*(ft|lb|ea|pc|net tons|cwt))?(?:\s+.*)?$/i;
    const match = input.match(pattern);
    if (!match) return null;
    const rawNumber = match[1];
    let unit = match[2] || null;
    if (unit) unit = unit.toLowerCase();
    const numberValue = Number(rawNumber.replace(/,/g, ''));
    return { numberValue, unit };
  }

  function parseDate(input){

    return input;
  }

  function parseNumber(input){

    return input;
  }

  function parseAlphaNumeric(input){

    return input;
  }



  const processBoxes = (boxes_to_process, pageNumber) => {
      const rawTextForProcessing = [];

      // Process each box
      for (const box of boxes_to_process) {
        const { id, rect, type } = box;
        // Mark this box as being processed
        //setProcessingBoxIds((prev) => new Set([...prev, id]));

          // Convert box coordinates to normalized coordinates (0-1 range)

          const canvasSize = pdfPageRefs.current[pageNumber-1]?.getCanvasSize() || { width: 0, height: 0 };
          const canvasWidth = canvasSize.width;
          const canvasHeight = canvasSize .height;
          const rotation = pageRotations;
          const zoomFactor = zoomPercentage / 100;

          // Coordinates are already in canvas space from processAllBoxes conversion
          const rectInCanvas = {
            top: rect.y * canvasHeight,
            left: rect.x * canvasWidth,
            width: rect.width * canvasWidth,
            height: rect.height * canvasHeight
          };

          // Transform based on rotation
          const normalizedRect = transformBoundingBox(
            {
              top: rect.y,
              left: rect.x,
              width: rect.width,
              height: rect.height
            },
            rotation,
            1,
            1
          );

          // Normalize to 0-1 range for query
          // const normalizedRect = {
          //   left: transformedRect.left / canvasWidth,
          //   top: transformedRect.top / canvasHeight,
          //   width: transformedRect.width / canvasWidth,
          //   height: transformedRect.height / canvasHeight
          // };

          // Query the TextractRBush spatial index
          const overlapRatio = overlapPercent/100;
          const pageSize = { width: canvasWidth, height: canvasHeight };
          // Explicitly pass the rotation parameter and zoomFactor to ensure consistent coordinate transformations

          const results = textractRBush.query(normalizedRect, .2, pageSize, pageNumber, rotation, zoomFactor);
          if(type === 'quantity'){
            results.forEach((item) => {
              if(item?.text?.trim() === '') return;
              const parsedQuantity = parseQuantity(item.text);
              if (parsedQuantity) {
                item.text = parsedQuantity.numberValue;
                item.unit = parsedQuantity.unit;
              }
            });
          }else if(BOX_TYPES[type]?.regex ){
            results.forEach((item) => {
              if(item?.text?.trim() === '') return;
              
              BOX_TYPES[type].regex.forEach((regexObj) => {
                switch(regexObj.type){
                  case 'replace':
                    item.text = item.text.replace(regexObj.regex, regexObj.substiture);
                    break;
                  case 'remove':
                    item.text = item.text.replace(regexObj.regex, '');
                    break;
                  case 'extract':
                    item.text = item.text.match(regexObj.regex)[0];
                    break;
                }
              });
            });
          }

          const averageX = results.reduce((sum, item) => sum + item.boundingBox.x, 0) / results.length;
          rawTextForProcessing.push({ id, type, textRows: results, averageX, rect: box.rect, pageSize });
      }
      let masterArr = [];


      //check for multiple boxes of same type
      const boxInfoForType = {};
      let hasMultipleBoxes = false;
      let arrTemp = [];
      const hieghestX = {};
      for (let key in BOX_TYPES) {
        arrTemp = rawTextForProcessing.filter((item)=>{
          if(key === item.type){
            hieghestX[key] = Math.max(hieghestX[key] || 0, item.averageX);
            return true;
          }
          return false;
        });
        if(arrTemp.length > 1){
          hasMultipleBoxes = hasMultipleBoxes|| checkForParallelBoxes(arrTemp);
        }
        boxInfoForType[key] = {boxCount:arrTemp.length, hieghestX: hieghestX[key] };
      }



      const processedArrays = {};
      //This marks the x position after which a new map starts hence a new row in master array
      let delimiterX = Infinity;
      const masterArrForMultipleColumns = [[],[]];

      if(hasMultipleBoxes){

        for(let key in boxInfoForType){
          if(boxInfoForType[key].boxCount > 1){
            delimiterX = Math.min(boxInfoForType[key].hieghestX, delimiterX);
          }
        }

        //create a master array using with x and y
        rawTextForProcessing.forEach((boxData) => {
          boxData.textRows.forEach((row) => {
            //Assumption ass we are extracting tabular data all texts in a box would have a very similar x hence we can take the boxes avg x
            if(row.boundingBox.x+10 < delimiterX){
              masterArrForMultipleColumns[0].push(row.boundingBox.y);
            }else{
              masterArrForMultipleColumns[1].push(row.boundingBox.y);
            }
          });
        })
        //Sort on y
        const zoomFactor = zoomPercentage / 100;
        masterArrForMultipleColumns[0].sort((a, b) => a - b);
        masterArrForMultipleColumns[0] = groupAndAverage(masterArrForMultipleColumns[0], 2*zoomFactor)
        masterArrForMultipleColumns[1].sort((a, b) => a - b);
        masterArrForMultipleColumns[1] = groupAndAverage(masterArrForMultipleColumns[1], 2*zoomFactor)

        rawTextForProcessing.forEach((boxData)=>{
          if(!processedArrays[boxData.type]) {
            processedArrays[boxData.type] = new Array(masterArrForMultipleColumns[0].length + masterArrForMultipleColumns[1].length).fill('');
          };
          
          if(boxData.type === 'quantity'){
            processedArrays['qtyUnit'] = new Array(masterArrForMultipleColumns[0].length + masterArrForMultipleColumns[1].length).fill(null);
          }
          if(boxData.averageX + 10 < delimiterX){
            boxData.textRows.forEach((row) => {
              const closestIndex = getClosestIndex(masterArrForMultipleColumns[0], row.boundingBox.y);
              processedArrays[boxData.type][closestIndex] = row.text;
              if(boxData.type === 'quantity' && row.unit){
                processedArrays['qtyUnit'][closestIndex] = row.unit;
              }
            });
          }else{
            boxData.textRows.forEach((row) => {
              const closestIndex = getClosestIndex(masterArrForMultipleColumns[1], row.boundingBox.y);
              processedArrays[boxData.type][masterArrForMultipleColumns[0].length + closestIndex] = row.text;
              if(boxData.type === 'quantity' && row.unit){
                processedArrays['qtyUnit'][masterArrForMultipleColumns[0].length + closestIndex] = row.unit;
              }
            });
          }

        });

      }else{
        rawTextForProcessing.forEach((boxData) => {
          boxData.textRows.forEach((row) => {
            masterArr.push(row.boundingBox.y);
          });
        })
        const zoomFactor = zoomPercentage / 100;
        masterArr.sort((a, b) => a - b);
        masterArr = groupAndAverage(masterArr, 3*zoomFactor)

        rawTextForProcessing.forEach(
          (boxData) => {
            if(!processedArrays[boxData.type]) {
              processedArrays[boxData.type] = new Array(masterArr.length).fill('');
            };
            if(boxData.type === 'quantity'){
              processedArrays['qtyUnit'] = new Array(masterArr.length).fill(null);
            }

            boxData.textRows.forEach((row) => {
              const closestIndex = getClosestIndex(masterArr, row.boundingBox.y);
              processedArrays[boxData.type][closestIndex] = row.text;
              if(boxData.type === 'quantity' && row.unit){
                processedArrays['qtyUnit'][closestIndex] = row.unit;
              }
            });

          }
        )
      }

      //setDisplayArrays( processedArrays);

      //setRawText(rawTextForProcessing);
      return {
        displayArrays: processedArrays,
        rowText: rawTextForProcessing
      }
  }

  function checkForParallelBoxes(arrRects) {
    const tolerance = 3 *zoomPercentage/100;
    let retValue = false;
    arrRects.forEach(({rect, pageSize}, i)=>{
      const from = rect.y*pageSize.height - tolerance;
      const to = rect.y*pageSize.height + rect.height*pageSize.height - tolerance;

      for(let index = i+1; index < arrRects.length; index++){
        const rect = {x: arrRects[index].rect.x*arrRects[index].pageSize.width, 
                      y: arrRects[index].rect.y*arrRects[index].pageSize.height, 
                      width: arrRects[index].rect.width*arrRects[index].pageSize.width, 
                      height: arrRects[index].rect.height*arrRects[index].pageSize.height};
        
        if( rect.y >= from && rect.y <= to){
          retValue = true;
          break;
        }
      }
    })
    return retValue;
  }


  function transformBoundingBox({ top, left, width, height }, rotation, canvasWidth, canvasHeight) {
    const rot = ((rotation % 360) + 360) % 360;
    let newLeft, newTop;
    switch (rot) {
      case 0:
        newLeft = left;
        newTop  = top;
        break;
      case 90:
        newLeft = canvasHeight - top - height;
        newTop  = left;
        break;
      case 180:
        newLeft = canvasWidth  - left  - width;
        newTop  = canvasHeight - top   - height;
        break;
      case 270:
        newLeft = top;
        newTop  = canvasWidth  - left  - width;
        break;
      default:
        throw new Error('Rotation must be a multiple of 90°');
    }
    return { left: newLeft, top: newTop, width, height };
  }


  function getClosestIndex(arr, target) {
    let left = 0;
    let right = arr.length - 1;
    while (left < right) {
        const mid = Math.floor((left + right) / 2);
        if (arr[mid] === target) return mid;
        if (arr[mid] < target) left = mid + 1;
        else right = mid;
    }
    // left is now the insertion point
    if (left === 0) return 0;
    // compare which neighbor is closer
    return (Math.abs(arr[left] - target) < Math.abs(arr[left - 1] - target))
        ? left
        : left - 1;
}

  function groupAndAverage(arr, tolerance) {
    const result = [];
    if (arr.length === 0) return result;
    let group = [arr[0]];
    for (let i = 1; i < arr.length; i++) {
      if (arr[i] - arr[i - 1] <= tolerance) {
        group.push(arr[i]);
      } else {
        const sum = group.reduce((a, b) => a + b, 0);
        result.push(sum / group.length);
        group = [arr[i]];
      }
    }
    const sum = group.reduce((a, b) => a + b, 0);
    result.push(sum / group.length);
    return result;
  }


  function groupAndAverageWithDelimiter(arr, delimiterX, toleranceX = 10, tolerance = 2) {
    const result = [];
    if (arr.length === 0) return result;
    let group = [arr[0]];
    for (let i = 1; i < arr.length; i++) {
      if (arr[i] - arr[i - 1] <= tolerance) {
        group.push(arr[i]);
      } else {
        const sum = group.reduce((a, b) => a + b, 0);
        result.push(sum / group.length);
        group = [arr[i]];
      }
    }
    const sum = group.reduce((a, b) => a + b, 0);
    result.push(sum / group.length);
    return result;
  }

  function detectRows(rect) {
    // Try to use high-resolution canvas for better text detection
    const useHiResCanvas = hiResCanvasRef.current !== null;
    const canvas = useHiResCanvas ? hiResCanvasRef.current : canvasRef.current;

    if (!canvas) return [];

    const ctx = canvas.getContext('2d', { willReadFrequently: true });
    if (!ctx) return [];

    // Use appropriate scale based on which canvas we're using
    const currentScale = useHiResCanvas ? scale * 3 : scale;

    const sx = Math.floor(rect.x * currentScale);
    const sy = Math.floor(rect.y * currentScale);
    const sw = Math.floor(rect.width * currentScale);
    const sh = Math.floor(rect.height * currentScale);

    const imageData = ctx.getImageData(sx, sy, sw, sh);
    const data = imageData.data;

    const width = imageData.width;
    const height = imageData.height;

    // Log which canvas we're using for row detection
    logOcr(`Detecting rows using ${useHiResCanvas ? 'high-resolution' : 'standard'} canvas`, {
      canvasType: useHiResCanvas ? 'high-resolution' : 'standard',
      scale: currentScale,
      dimensions: { width, height },
      boxRect: { x: sx, y: sy, width: sw, height: sh }
    });

    const rowVariations = new Array(height).fill(0);

    // Step 1: Calculate variation per row
    for (let y = 0; y < height; y++) {
      let lastGray = -1;
      let totalVariation = 0;

      for (let x = 0; x < width; x++) {
        const idx = (y * width + x) * 4;
        const r = data[idx];
        const g = data[idx + 1];
        const b = data[idx + 2];
        const gray = 0.299 * r + 0.587 * g + 0.114 * b;

        if (lastGray >= 0) {
          totalVariation += Math.abs(gray - lastGray);
        }
        lastGray = gray;
      }

      rowVariations[y] = totalVariation / (width - 1);
    }

    // Step 2: Smooth the variations a little (simple moving average)
    const smoothedVariations = rowVariations.map((_, idx) => {
      let sum = 0,
        count = 0;
      for (let offset = -1; offset <= 1; offset++) {
        const neighborIdx = idx + offset;
        if (neighborIdx >= 0 && neighborIdx < height) {
          sum += rowVariations[neighborIdx];
          count++;
        }
      }
      return sum / count;
    });

    // Step 3: Calculate dynamic threshold
    const avgVariation =
      smoothedVariations.reduce((sum, v) => sum + v, 0) / height;
    const variationThreshold = avgVariation * 0.5;

    // Step 4: Find row midpoints
    const rowPositions = [];
    let inTextBlock = false;
    let blockStart = 0;

    for (let y = 1; y < height - 1; y++) {
      const isText = smoothedVariations[y] > variationThreshold;

      if (!inTextBlock && isText) {
        inTextBlock = true;
        blockStart = y;
      }

      if (inTextBlock && !isText) {
        if (y - blockStart > 5) {
          const midY = Math.floor(blockStart + (y - blockStart) / 2);

          // Use native coordinates without conversion
          const rowY = sy + midY;
          const rowX = sx + Math.floor(width / 2);

          // Include both X and Y coordinates in their native coordinate system
          rowPositions.push({
            y: rowY,  // Native Y position (high-res or standard)
            x: rowX   // Native X position (high-res or standard)
          });
        }
        inTextBlock = false;
      }
    }

    // Last row edge case
    if (inTextBlock && height - blockStart > 5) {
      const midY = Math.floor(blockStart + (height - blockStart) / 2);

      // Use native coordinates without conversion
      const rowY = sy + midY;
      const rowX = sx + Math.floor(width / 2);

      rowPositions.push({
        y: rowY,
        x: rowX
      });
    }

    // If no rows detected but significant content, fallback
    if (rowPositions.length === 0 && avgVariation > 5) {
      // Use native coordinates without conversion
      const rowY = sy + Math.floor(height / 2);
      const rowX = sx + Math.floor(width / 2);

      rowPositions.push({
        y: rowY,
        x: rowX
      });
    }

    // Log the detected row positions
    logOcr(`Detected ${rowPositions.length} text rows using native coordinates`, {
      rowCount: rowPositions.length,
      usingHiRes: useHiResCanvas,
      coordinateSystem: useHiResCanvas ? 'high-resolution' : 'standard',
      firstRowY: rowPositions.length > 0 ? rowPositions[0].y : null,
      lastRowY: rowPositions.length > 0 ? rowPositions[rowPositions.length - 1].y : null,
      avgVariation
    });

    return rowPositions;
  }

  function getFromId(id, array) {
    return array.find((item) => item.id === id);
  }

  function parseOpenAIResponse(response) {
    if (typeof response !== 'string') {
      console.error('Invalid input: Response must be a string');
      return null;
    }

    try {
      if (response.startsWith('```json')) {
        response = response
          .replace(/^```json/, '')
          .replace(/```$/, '')
          .trim();
      } else if (response.startsWith('```')) {
        response = response.replace(/^```/, '').replace(/```$/, '').trim();
      }

      const parsed = JSON.parse(response);
      return parsed;
    } catch (error) {
      console.error('Failed to parse response:', error.message);
      return null;
    }
  }

  // Note: Unused functions have been removed to clean up the code

  // Note: Unused mergeExtractedData function has been removed

  /**
   * Simple function to compile data from scratch
   * @param {Array} inputData - Array of box data objects
   * @returns {Object} - Compiled data organized by box type
   */
  function compileDataByYNew(inputData) {
    // Create a result object with empty arrays for each box type
    const result = createEmptyDataArrays(1); // Start with 1 row, we'll expand as needed

    inputData.forEach(box => {
      result[box.type+"Arr"] = box.textRows;
    });

    return result;
  }

  // Original function now calls the new implementation
  function compileDataByY(inputData) {
    // Use the new implementation
    return compileDataByYNew(inputData);
  }

  // Handle rotation
  const handleRotateClockwise = () => {
    const newRotation = (rotation + 90) % 360;
    setRotation(newRotation);
  };

  const handleRotateCounterClockwise = () => {
    const newRotation = (rotation - 90 + 360) % 360;
    setRotation(newRotation);
  };

  const exportToCSV = () => {
    // Function to escape CSV values (wrap in quotes if contains comma or quotes)
    const escapeCSV = (value) => {
      if (!value) return '';
      // Replace double quotes with two double quotes (CSV escaping standard)
      const escaped = value.replace(/"/g, '""');
      // If value contains comma, newline, or quotes, wrap in quotes
      return escaped.includes(',') ||
        escaped.includes('"') ||
        escaped.includes('\n')
        ? `"${escaped}"`
        : escaped;
    };

    // Add headers row using our configuration
    const headers =
      'Page,' +
      Object.values(BOX_TYPES)
        .map((type) => type.label)
        .join(',') +
      '\n';

    // Collect rows from all pages
    let allRows = [];

    // First add current page data if it exists and isn't in allExtractedData
    if (extractedData?.descriptionArr?.length > 0) {
      const currentPageKey = `page${pageNumber}`;
      const isCurrentPageInAll =
        allExtractedData[currentPageKey] &&
        JSON.stringify(allExtractedData[currentPageKey]) ===
          JSON.stringify(extractedData);

      if (!isCurrentPageInAll) {
        extractedData.descriptionArr.forEach((_, index) => {
          const row = { page: pageNumber };

          // Add data for each box type
          Object.keys(BOX_TYPES).forEach((boxType) => {
            const arrayName = `${boxType}Arr`;
            row[boxType] = extractedData[arrayName]?.[index] || '';
          });

          allRows.push(row);
        });
      }
    }

    // Then add data from all pages
    Object.entries(allExtractedData).forEach(([pageKey, data]) => {
      if (!data || !data.descriptionArr || data.descriptionArr.length === 0)
        return;

      const pageNum = pageKey.replace('page', '');

      data.descriptionArr.forEach((_, index) => {
        const row = { page: pageNum };

        // Add data for each box type
        Object.keys(BOX_TYPES).forEach((boxType) => {
          const arrayName = `${boxType}Arr`;
          row[boxType] = data[arrayName]?.[index] || '';
        });

        allRows.push(row);
      });
    });

    // Convert rows to CSV format
    const dataRows = allRows
      .map((row) => {
        // Start with the page number
        let csvRow = escapeCSV(row.page);

        // Add each box type in the same order as the headers
        Object.keys(BOX_TYPES).forEach((boxType) => {
          csvRow += ',' + escapeCSV(row[boxType] || '');
        });

        return csvRow;
      })
      .join('\n');

    const csvContent = headers + dataRows;

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'extracted_data.csv';
    a.click();
    URL.revokeObjectURL(url);

    logExtract('CSV export completed', {
      totalRows: allRows.length,
      fileSize: Math.round(csvContent.length / 1024) + ' KB',
      fileName: 'extracted_data.csv',
      timestamp: new Date().toISOString(),
    });
  };

  // Handle undo box action
  const handleUndoBox = () => {
    if (boxes.length > 0) {
      const lastBox = boxes[boxes.length - 1];

      // Remove the box
      setBoxes((prev) => prev.slice(0, -1));

      // Remove its ID from processed box IDs
      setProcessedBoxIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(lastBox.id);
        return newSet;
      });

      // Remove from processing box IDs if it's being processed
      setProcessingBoxIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(lastBox.id);
        return newSet;
      });

      // Update raw box data
      const pageKey = `page${pageNumber}`;

      // First update the raw box data
      setRawBoxData((prev) => {
        // Get current page data
        const pageData = prev[pageKey] || [];

        // Filter out this box
        const updatedPageData = pageData.filter(
          (item) => item.id !== lastBox.id
        );

        // Create updated raw box data
        const newRawBoxData = {
          ...prev,
          [pageKey]: updatedPageData,
        };

        // Update the ref with the latest data
        rawBoxDataRef.current = newRawBoxData;

        // Return updated raw box data
        return newRawBoxData;
      });

      // Now get ALL raw data for this page to compile
      setTimeout(() => {
        // Get the latest raw data from the ref
        const allPageData = rawBoxDataRef.current[pageKey] || [];

        // Compile data from ALL boxes on this page
        const compiledData = compileDataByY(allPageData);

        // Update the current page data
        setExtractedData(compiledData);

        // Update the all extracted data store
        setAllExtractedData((prev) => ({
          ...prev,
          [pageKey]: compiledData,
        }));

        logBox(`UI updated after removing box ${lastBox.id}`, {
          boxId: lastBox.id,
          boxType: lastBox.type,
          pageNumber,
          remainingBoxes: boxes.length - 1,
          timestamp: new Date().toISOString(),
        });
      }, 10); // Slightly longer timeout to ensure state updates have completed

      logBox(`Box removed from page ${pageNumber}`, {
        boxId: lastBox.id,
        boxType: lastBox.type,
        position: lastBox.rect,
        remainingBoxes: boxes.length - 1,
        timestamp: new Date().toISOString(),
      });
    }
  };

  // Update error message in parent component
  useEffect(() => {
    if (onError && errorMessage) {
      onError(errorMessage);
    }
  }, [errorMessage, onError]);

  // Utility functions for viewport detection
  const getVisiblePages = () => Array.from(visiblePages).sort((a, b) => a - b);
  const getFullyVisiblePages = () => Array.from(fullyVisiblePages).sort((a, b) => a - b);
  const isPageVisible = (pageIndex) => visiblePages.has(pageIndex);
  const isPageFullyVisible = (pageIndex) => fullyVisiblePages.has(pageIndex);
  const getVisibilityInfo = () => ({
    visiblePages: getVisiblePages(),
    fullyVisiblePages: getFullyVisiblePages(),
    totalPages: pdfPages.length,
    visibleCount: visiblePages.size,
    fullyVisibleCount: fullyVisiblePages.size
  });

  // Expose methods to parent component via ref
  React.useImperativeHandle(ref, () => ({
    handleUndoBox,
    extractImages,
    exportCSV,
    setCurrentBoxType: (type) => setCurrentBoxType(type),
    // Viewport detection methods
    getVisiblePages,
    getFullyVisiblePages,
    isPageVisible,
    isPageFullyVisible,
    getVisibilityInfo,

    // Methods for AWS Textract integration
    getBoxes: () => boxes,
    getPageNumber: () => pageNumber,
    getCanvasWidth: () => canvasRef.current?.width || 0,
    getCanvasHeight: () => canvasRef.current?.height || 0,
    updateExtractedData: (textractExtractedData) => {
      // Convert Textract extracted data to our format
      const pageKey = `page${pageNumber}`;

      // Initialize arrays for each box type
      const newPageData = {};
      Object.keys(BOX_TYPES).forEach(type => {
        newPageData[`${type}Arr`] = [];
        newPageData[`${type}Ids`] = [];
      });

      // Add data from each box
      Object.entries(textractExtractedData).forEach(([boxId, data]) => {
        const boxType = data.type;

        // Add to the appropriate arrays
        if (boxType && BOX_TYPES[boxType]) {
          newPageData[`${boxType}Arr`].push(data.text || '');
          newPageData[`${boxType}Ids`].push(boxId);

          // Mark this box as processed
          setProcessedBoxIds(prev => {
            const newSet = new Set(prev);
            newSet.add(boxId);
            return newSet;
          });
        }
      });

      // Update extracted data for this page
      setExtractedData(prev => ({
        ...prev,
        [pageKey]: newPageData
      }));

      // Update all extracted data
      setAllExtractedData(prev => ({
        ...prev,
        [pageKey]: newPageData
      }));

      // Notify parent that we have extracted data
      // if (onExtractedDataChange) {
      //   onExtractedDataChange(true);
      // }
    },
    // Method to receive and initialize TextractRBush with Textract data
    initializeTextractRBush: (textractData) => {
      if (!textractData || !textractData.Blocks) {
        return null;
      }
      if(!isImageBasedPdf){
        return null;
      }

      try {
        // Create a new TextractRBush instance
        const textractRBushInstance = TextractRBush();

        // Initialize with Textract data
        textractRBushInstance.initialize(textractData);

        // Store the instance in state
        setTextractRBush(textractRBushInstance);
        setTextractRBushInitialized(true);

        return textractRBushInstance;
      } catch (error) {
        console.error('Failed to initialize TextractRBush', error);
        return null;
      }
    }
  }));

  useEffect(()=>{
    if(onExtractedDataChange && displayArrays && Object.keys(displayArrays).length > 0 && Object.keys(displayArrays[Object.keys(displayArrays)[0]]).length > 0)  {
      onExtractedDataChange(true);
    }else{
      onExtractedDataChange(false);
    }
  },[displayArrays])

  // Effect to handle when TextractRBush becomes available
  // useEffect(() => {
  //   if (textractRBush) {
  //     // Process boxes if requested
  //     if (processWhenReady && boxes.length > 0) {
  //       const currentPageKey = `page${pageNumber}`;
  //       const tempBoxes = {};
  //       tempBoxes[currentPageKey] = boxes;

  //       logBox(`Saving boxes for page ${pageNumber}`, {
  //         pageNumber,
  //         boxCount: boxes.length,
  //         boxTypes: boxes.reduce((acc, box) => {
  //           acc[box.type] = (acc[box.type] || 0) + 1;
  //           return acc;
  //         }, {}),
  //       });

  //       const all_boxes = {...allBoxes, ...tempBoxes };
  //       setAllBoxes((prev) => {
  //         return {...prev, ...tempBoxes };
  //       });
  //       processAllBoxes(all_boxes);

  //       setProcessWhenReady(false);
  //     }
  //   }
  // }, [textractRBush, processWhenReady, boxes, pageNumber, allBoxes, processAllBoxes, logBox]);


  // Add keyboard event listener to close magnifying glass with Esc key
  React.useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape' && showMagnifyingGlass) {
        setShowMagnifyingGlass(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [showMagnifyingGlass]);

  useEffect(() => {
    if(updatedScrollViewPort && containerRef.current && scrollableContainerRef.current){
      setScrollViewPort(updatedScrollViewPort);
      const {width, height} = containerRef.current.getBoundingClientRect();
      scrollableContainerRef.current.scrollLeft = updatedScrollViewPort.x * width;
      scrollableContainerRef.current.scrollTop = updatedScrollViewPort.y * height;
    }
  }, [updatedScrollViewPort]);

  const edgeThreshold = 60;
  const autoScrollSpeed = 10;

  const [vScrollSpeed, setVScrollSpeed] = useState(0);
  const vScrollSpeedRef = useRef(0);
  vScrollSpeedRef.current = vScrollSpeed;
  const [hScrollSpeed, setHScrollSpeed] = useState(0);
  const hScrollSpeedRef = useRef(0);
  hScrollSpeedRef.current = hScrollSpeed;

  const handleMouseOut = (event) => {
    setHScrollSpeed(0);
    setVScrollSpeed(0);
    setdoAutoScroll(false);
  };

  const handleMouseMove = (event) => {
    if(!doAutoScroll){
      setHScrollSpeed(0);
      setVScrollSpeed(0);
      return;
    }
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    if(x < edgeThreshold){
      setHScrollSpeed(autoScrollSpeed*(x - edgeThreshold)/edgeThreshold);
    }else if(x > rect.width - edgeThreshold){
      setHScrollSpeed(autoScrollSpeed*(x - rect.width + edgeThreshold)/edgeThreshold);
    }else{
      setHScrollSpeed(0);
    }

    if(y < edgeThreshold){
      setVScrollSpeed(autoScrollSpeed*(y - edgeThreshold)/edgeThreshold);
    }else if(y > rect.height - edgeThreshold){
      setVScrollSpeed(autoScrollSpeed*(y - rect.height + edgeThreshold)/edgeThreshold);
    }else{
      setVScrollSpeed(0);
    }
  };

  // Add these useEffect hooks to update the refs when state changes
  useEffect(() => {
    vScrollSpeedRef.current = vScrollSpeed;
  }, [vScrollSpeed]);

  useEffect(() => {
    hScrollSpeedRef.current = hScrollSpeed;
  }, [hScrollSpeed]);

  const rafIdRef = useRef(null);
  const animateScroll = () => {
    rafIdRef.current = requestAnimationFrame(animateScroll);
    // Check if the container exists
    if (!scrollableContainerRef.current) {
      console.warn('scrollableContainerRef.current is null');
      return;
    }
    
    // Apply scrolling if speeds are non-zero
    if (hScrollSpeedRef.current) {
      scrollableContainerRef.current.scrollLeft += hScrollSpeedRef.current;
    }
    
    if (vScrollSpeedRef.current) {
      scrollableContainerRef.current.scrollTop += vScrollSpeedRef.current;
    }
  };

  // Start animation frame after component is mounted
  useEffect(() => {
    // Wait a small delay to ensure the ref is attached
    const timeoutId = setTimeout(() => {
      if (scrollableContainerRef.current) {
        rafIdRef.current = requestAnimationFrame(animateScroll);
      } else {
        console.warn('scrollableContainerRef.current is null after timeout');
      }
    }, 100);
    
    return () => {
      clearTimeout(timeoutId);
      if (rafIdRef.current) {
        cancelAnimationFrame(rafIdRef.current);
      }
    };
  }, []);

  const handleScroll = (e) => {
    if(!containerRef.current || !scrollableContainerRef.current) return;
    const containerRect = containerRef.current.getBoundingClientRect();
    const calculatedScrollViewPort = {
      x: scrollableContainerRef.current.scrollLeft/containerRect.width,
      y: scrollableContainerRef.current.scrollTop/containerRect.height,
      width: scrollableContainerRef.current.clientWidth/containerRect.width,
      height: scrollableContainerRef.current.clientHeight/containerRect.height,
    }
    setScrollViewPort(calculatedScrollViewPort);
  };

  useEffect(() => {
    if (!containerRef.current || !scrollableContainerRef.current) return;
    const observer = new ResizeObserver(entries => {
      for (let entry of entries) {
        if (!scrollableContainerRef || !scrollableContainerRef.current) break;
        const { width, height } = entry.contentRect;
        const calculatedScrollViewPort = {
          x: scrollableContainerRef.current.scrollLeft/width,
          y: scrollableContainerRef.current.scrollTop/height,
          width: scrollableContainerRef.current.clientWidth/width,
          height: scrollableContainerRef.current.clientHeight/height,
        }
        setScrollViewPort(calculatedScrollViewPort);
      }
    });
    observer.observe(containerRef.current);
    return () => observer.disconnect();
  }, []);

  
  const hasDescriptionBox = useMemo(() => {
    return allBoxes.some(pageBoxes => 
      pageBoxes?.some(box => box.type === 'description')
    );
  }, [allBoxes]);

  // Function to calculate button position relative to the OCR container
  const updateButtonPosition = useCallback(() => {
    if (scrollableContainerRef.current) {
      const rect = scrollableContainerRef.current.getBoundingClientRect();
      setButtonPosition({
        left: rect.left + rect.width - 220, // button width + margin
        top: rect.bottom - 60 // button height + margin
      });
    }
  }, []);

  const [buttonPosition, setButtonPosition] = useState({ left: 0, top: 0 });

  // Update position on resize
  useEffect(() => {
    updateButtonPosition();
    
    const handleResize = () => updateButtonPosition();
    window.addEventListener('resize', handleResize);
    
    const observer = new ResizeObserver(updateButtonPosition);
    if (scrollableContainerRef.current) {
      observer.observe(scrollableContainerRef.current);
    }
    
    return () => {
      window.removeEventListener('resize', handleResize);
      observer.disconnect();
    };
  }, [updateButtonPosition]);


  return (
    <div 
      style={{width:'100%', height:'100%',position:'relative'}}
    >
      {/* Toolbar is now handled by the parent component */}
      

      {errorMessage && <p className={clsx(styles.alert, styles.alertError)}>{errorMessage}</p>}

      <div className={styles.scrollableContainer}
       ref={scrollableContainerRef}
       onScroll={handleScroll}
       onMouseMove={handleMouseMove}
       onMouseOut={handleMouseOut}
       >
        <div ref={containerRef} style={{width:'max-content', margin:'0 auto'}}>
      { (numPages && pdfPages?.length > 0) ?
      pdfPages.map((page, index )=>{
        return (
          <div
            className={styles.pdfPageContainer}
            key={index}
            data-page-index={index}
          >
            <PdfPage
              key={index}
              index={index}
              ref={el => {
                pdfPageRefs.current[index] = el}
              }
              onMount={onPdfPageMounted}
              page={page}
              scale={scale}
              fineRotations={canvasRotation}
              parentShowMagnifyingGlass={showMagnifyingGlass}
              snapToGrid={snapToGrid}
              gridOpacity={gridOpacity}
              autoSelectColumns={autoDetectColumns}
              textractRBush={textractRBush}
              overlapPercent={overlapPercent}
            />
          </div>);
      }):<></>
      }
      </div>
      </div>
      <button 
      className={styles.btnExtract}
        style={{
          // position: 'fixed',
          // left: buttonPosition.left,
          //top: buttonPosition.top,
          //  bottom: 35,
          cursor: hasDescriptionBox && textractRBush ? 'pointer' : 'not-allowed'
        }} 
        onClick={extractImages}
        disabled={!hasDescriptionBox || !textractRBush}
      >
        EXTRACT
      </button>
    </div>
  );
});

export default PdfTextExtractorOCR;
